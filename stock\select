#!/bin/sh

cd /home/<USER>/stock

while IFS= read -r line || [ -n "$line" ]; 
do
  echo "$line" >> query_$$.hql
done < $1

COUNTRY_CODE=$2
STORE_LIST=$3

/opt/spark_thrift_kyuubi/bin/beeline \
    -n phrubos \
    -w /home/<USER>/password_file \
    --silent=false \
    --outputformat=csv2 \
    --numberFormat=#.########## \
    --nullemptystring=true \
    -u "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" \
    --hivevar COUNTRY_CODE=$2 \
    --hivevar STORE_LIST=$3 \
    -f query_$$.hql > extract.txt

rm query_$$.hql
