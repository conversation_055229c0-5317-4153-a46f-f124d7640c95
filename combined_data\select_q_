#!/bin/sh

cd /home/<USER>/combined_data

while IFS= read -r line || [ -n "$line" ]; 
do
  echo $line >> query_$$.hql
done < $1

/opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u "****************************************************************************************************************************************************************************************************************************************" \
        -f "./query_$$.hql"

rm query_$$.hql

