cd /home/<USER>/15mins
/opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file \
    -u "******************************************************; \
    transportMode=https?kyuubi.engine.share.level.subdomain=cep_user_dc2sweekly; \
    spark.hadoop.fs.s3a.assumed.role.arn=arn:aws:iam:::role/role-cep-resource-sch; \
    spark.yarn.queue=kyuubi" -e "select * from poshu.t001 limit 10" \
    > /home/<USER>/15mins/output/15mins.csv