#!/bin/sh

# Change to the appropriate directory
cd /home/<USER>/item_sold

# Read the SQL file line by line and create temporary query file
while IFS= read -r line || [ -n "$line" ]; 
do
  echo "$line" >> "query_$$.hql"
done < "$1"

# Execute the query using the new connection parameters
/opt/spark_thrift_kyuubi/bin/beeline \
    -n phrubos \
    -p \
    -u "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" \
    -f "./query_$$.hql"

# Clean up temporary file
rm "query_$$.hql"
