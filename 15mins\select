# Change directory
cd /home/<USER>/15mins

while IFS= read -r line || [ -n "$line" ]; 
do
  echo $line >> query_$$.hql
done < $1


/opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file --silent=false --outputformat=csv2 --numberFormat=#.########## --nullemptystring=true -u "**************************************************************************************************************************************************************************************************************************" \
	-f query_$$.hql > extract.txt 

rm query_$$.hql

