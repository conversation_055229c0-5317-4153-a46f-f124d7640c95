cd /home/<USER>/item_sold_dotcom

while IFS="|" read countrycode storelistid storelist
	do
		echo 'COUNTRY:    ' $countrycode
		echo 'STORE LIST: ' $storelistid
		echo 'STORES:     ' $storelist
		./select select.sql $countrycode $storelist
		tail --silent -n +8 extract.txt > temp.txt ; mv temp.txt extract.txt
		head --silent -n -2 extract.txt > temp.txt ; mv temp.txt extract.txt
		cp extract.txt /home/<USER>/item_sold_dotcom/output/item_sold_dotcom_$countrycode$storelistid.csv
		
		# Zip the output CSV file
		zip /home/<USER>/item_sold_dotcom/output/item_sold_dotcom_$countrycode$storelistid.zip /home/<USER>/item_sold_dotcom/output/item_sold_dotcom_$countrycode$storelistid.csv

		# Clean up: Remove the original CSV file
		rm /home/<USER>/item_sold_dotcom/output/item_sold_dotcom_$countrycode$storelistid.csv

		rm extract.txt
	done < parameters_all_groups_HU.csv
