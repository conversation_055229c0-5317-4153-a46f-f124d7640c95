#!/bin/sh

# Change to the appropriate directory
cd /home/<USER>/item_sold

# Read the SQL file line by line and create temporary query file
while IFS= read -r line || [ -n "$line" ]; 
do
  echo "$line" >> "query_$.hql"
done < "$1"

# Execute the query using password file instead of interactive prompt
/opt/spark_thrift_kyuubi/bin/beeline \
    -n phrubos \
    -w /home/<USER>/password_file \
    -u "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" \
    -f "./query_$.hql"

# Clean up temporary file
rm "query_$.hql"
