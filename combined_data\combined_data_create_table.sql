UNCACHE TABLE IF EXISTS cal;
DROP VIEW IF EXISTS cal;
CACHE TABLE cal AS
SELECT dmtm_d_code, dtdw_day_desc_en FROM dm.dim_time_d WHERE dmtm_fw_code BETWEEN 'f2025w06'  AND 'f2025w06';

UNCACHE TABLE IF EXISTS hier;
DROP VIEW IF EXISTS hier;
CACHE TABLE hier AS
SELECT
    LPAD(div_code,4,"0") AS div_code,
    LPAD(dep_code,4,"0") AS dep_code,
    LPAD(sec_code,4,"0") AS sec_code,
    LPAD(grp_code,4,"0") AS grp_code,
    LPAD(sgr_code,4,"0") AS sgr_code,
    pmg
FROM tesco_analysts.hierarchy_spm
WHERE SUBSTRING(pmg, 1, 3) IN ('BWS','DAI','DRY','FRZ','HDL','HEA','PPD','PRO','SFB','SFM', 'SFP');

DROP TABLE IF EXISTS sch_analysts.tbl_ce_daily_combined_data_phrubos;

CREATE EXTERNAL TABLE sch_analysts.tbl_ce_daily_combined_data_phrubos
TBLPROPERTIES('external.table.purge'='TRUE')
STORED AS ORC
LOCATION "s3a://cep-sch-analysts-db/sch_analysts_external/tbl_ce_daily_combined_data_phrubos" AS
WITH base_sales AS (
    SELECT
        stores.cntr_code AS country,
        cast(stores.dmst_store_code as INT) AS store,
        sunit.dtdw_day_desc_en as day,
        hier.pmg AS pmg,
        cast(mstr.slad_tpnb as INT) AS tpnb,
        sunit.slsms_unit/1 AS sold_units,
        sunit.slsms_salex/1 AS sales_excl_vat
    FROM
        sal_store AS sunit
    JOIN
        dm.dim_artgld_details mstr
        ON
            mstr.slad_dmat_id = sunit.slsms_dmat_id AND
            mstr.cntr_id IN (1,2,4) AND
            mstr.cntr_id = sunit.slsms_cntr_id
    JOIN
        hier AS hier
        ON
            mstr.dmat_div_code = hier.div_code AND
            mstr.dmat_dep_code = hier.dep_code AND
            mstr.dmat_sec_code = hier.sec_code AND
            mstr.dmat_grp_code = hier.grp_code AND
            mstr.dmat_sgr_code = hier.sgr_code
    JOIN
        dm.dim_stores stores
        ON
            stores.cntr_id = sunit.slsms_cntr_id AND
            stores.dmst_store_id = sunit.slsms_dmst_id AND
            stores.cntr_id IN (1,2,4) AND
            stores.convenience IN ('Convenience', 'HM')
    WHERE
        sunit.slsms_unit > 0
),
dotcom_sales AS (
    SELECT
        stores.cntr_code AS country,
        cast(stores.dmst_store_code as INT) AS store,
        sunit.dtdw_day_desc_en as day,
        hier.pmg AS pmg,
        cast(mstr.slad_tpnb as INT) AS tpnb,
        sunit.slsms_unit/1 AS sold_units_dotcom
    FROM
        sal_dotcom AS sunit
    JOIN
        dm.dim_artgld_details mstr
        ON
            mstr.slad_dmat_id = sunit.sltrg_dmat_id AND
            mstr.cntr_id IN (1,2,4) AND
            mstr.cntr_id = sunit.sltrg_cntr_id
    JOIN
        hier AS hier
        ON
            mstr.dmat_div_code = hier.div_code AND
            mstr.dmat_dep_code = hier.dep_code AND
            mstr.dmat_sec_code = hier.sec_code AND
            mstr.dmat_grp_code = hier.grp_code AND
            mstr.dmat_sgr_code = hier.sgr_code
    JOIN
        dm.dim_stores stores
        ON
            stores.cntr_id = sunit.sltrg_cntr_id AND
            stores.dmst_store_id = sunit.sltrg_dmst_id AND
            stores.cntr_id IN (1,2,4) AND
            stores.convenience IN ('Convenience', 'HM')
),
stock_data AS (
    SELECT
        stores.cntr_code AS country,
        cast(stores.dmst_store_code as INT) AS store,
        stock.dtdw_day_desc_en as day,
        hier.pmg AS pmg,
        cast(mstr.slad_tpnb as INT) AS tpnb,
        stock.stock_unit/1 AS stock_units,
        stock.slstks_price AS item_price
    FROM
        sal_stock AS stock
    JOIN
        dm.dim_artgld_details mstr
        ON
            mstr.slad_dmat_id = stock.slstks_dmat_id AND
            mstr.cntr_id IN (1,2,4) AND
            mstr.cntr_id = stock.slstks_cntr_id
    JOIN
        hier AS hier
        ON
            mstr.dmat_div_code = hier.div_code AND
            mstr.dmat_dep_code = hier.dep_code AND
            mstr.dmat_sec_code = hier.sec_code AND
            mstr.dmat_grp_code = hier.grp_code AND
            mstr.dmat_sgr_code = hier.sgr_code
    JOIN
        dm.dim_stores stores
        ON
            stores.cntr_id = stock.slstks_cntr_id AND
            stores.dmst_store_id = stock.slstks_dmst_id AND
            stores.cntr_id IN (1,2,4) AND
            stores.convenience IN ('Convenience', 'HM')
    WHERE
        stock.stock_unit > 0
),
cases_data AS (
    SELECT
        CASE 
            WHEN cases.int_cntr_id = 4 THEN 'HU'
            WHEN cases.int_cntr_id = 2 THEN 'SK'
            WHEN cases.int_cntr_id = 1 THEN 'CZ'
        END AS country,
        cases.store,
        cases.dtdw_day_desc_en as day,
        hier.pmg AS pmg,
        cast(mstr.slad_tpnb as INT) AS tpnb,
        cases.cases_unit/1 AS cases_units
    FROM
        sal_cases AS cases
    JOIN
        dm.dim_artgld_details mstr
        ON
            mstr.cntr_id IN (1,2,4) AND
            mstr.cntr_id = cases.int_cntr_id AND
            mstr.slad_tpnb = cases.product
    JOIN
        hier AS hier
        ON
            mstr.dmat_div_code = hier.div_code AND
            mstr.dmat_dep_code = hier.dep_code AND
            mstr.dmat_sec_code = hier.sec_code AND
            mstr.dmat_grp_code = hier.grp_code AND
            mstr.dmat_sgr_code = hier.sgr_code
    WHERE
        cases.cases_unit > 0
)

SELECT
    b.country,
    b.store,
    b.day,
    b.pmg,
    b.tpnb,
    b.sold_units,
    b.sales_excl_vat,
    d.sold_units_dotcom,
    s.stock_units,
    s.item_price,
    c.cases_units
FROM
    base_sales b
LEFT JOIN dotcom_sales d
    ON b.country = d.country
    AND b.store = d.store
    AND b.day = d.day
    AND b.pmg = d.pmg
    AND b.tpnb = d.tpnb
LEFT JOIN stock_data s
    ON b.country = s.country
    AND b.store = s.store
    AND b.day = s.day
    AND b.pmg = s.pmg
    AND b.tpnb = s.tpnb
LEFT JOIN cases_data c
    ON b.country = c.country
    AND b.store = c.store
    AND b.day = c.day
    AND b.pmg = c.pmg
    AND b.tpnb = c.tpnb
ORDER BY
    b.country,
    b.store,
    b.day,
    b.pmg,
    b.tpnb;
