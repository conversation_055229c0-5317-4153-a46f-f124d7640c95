UNCACHE TABLE IF EXISTS cal;
DROP VIEW IF EXISTS cal;
CACHE TABLE cal AS
SELECT dmtm_d_code, dtdw_day_desc_en FROM dm.dim_time_d WHERE dmtm_fw_code BETWEEN 'f2025w21'  AND 'f2025w24';

-- select count(*) from cal;
-- select * from cal limit 10;


UNCACHE TABLE IF EXISTS sal;
DROP VIEW IF EXISTS sal;
CACHE TABLE sal AS
SELECT
	sunit.slsms_cntr_id,
	sunit.slsms_dmat_id,
	sunit.slsms_dmst_id,
	cal.dtdw_day_desc_en,
	SUM(sunit.slsms_unit) AS slsms_unit,
	SUM(sunit.slsms_salex) AS slsms_salex
FROM
	cal AS cal
join
	dw.sl_sms sunit
	ON
		cal.dmtm_d_code = sunit.part_col AND
		--sunit.slsms_cntr_id IN (${COUNTRY_ID_LIST})
		sunit.slsms_cntr_id IN (1,2,4)
GROUP BY
	sunit.slsms_cntr_id,
	sunit.slsms_dmat_id,
	sunit.slsms_dmst_id,
	cal.dtdw_day_desc_en;

-- select count(*) from sal;
-- select * from sal limit 10;


UNCACHE TABLE IF EXISTS hier;
DROP VIEW IF EXISTS hier;
CACHE TABLE hier AS
SELECT
	LPAD(div_code,4,"0") AS div_code,
	LPAD(dep_code,4,"0") AS dep_code,
	LPAD(sec_code,4,"0") AS sec_code,
	LPAD(grp_code,4,"0") AS grp_code,
	LPAD(sgr_code,4,"0") AS sgr_code,
	pmg
FROM tesco_analysts.hierarchy_spm
WHERE SUBSTRING(pmg, 1, 3) IN ('BWS','DAI','DRY','FRZ','HDL','HEA','PPD','PRO','SFB','SFM', 'SFP');

-- select count(*) from hier;
-- select * from hier limit 10;


DROP TABLE IF EXISTS sch_analysts.tbl_ce_daily_avg_sales_phrubos;

CREATE EXTERNAL TABLE sch_analysts.tbl_ce_daily_avg_sales_phrubos
TBLPROPERTIES('external.table.purge'='TRUE')
STORED AS ORC
LOCATION "s3a://cep-sch-analysts-db/sch_analysts_external/tbl_ce_daily_avg_sales_phrubos" AS
SELECT 
	stores.cntr_code AS country,
	cast(stores.dmst_store_code as INT) AS store,
	sunit.dtdw_day_desc_en as day,
	hier.pmg AS pmg,
	cast(mstr.slad_tpnb as INT) AS tpnb,
	--mstr.slad_tpn AS tpn,
	--mstr.own_brand as ownbrand,
--	mstr.slad_long_des AS product_name,
	--mstr.slad_unit AS unit_type,
	--mstr.slad_case_size AS case_capacity,
	--mstr.slad_net_weight AS weight,
	sunit.slsms_unit/1 AS sold_units,
	sunit.slsms_salex/1 AS sales_excl_vat 
FROM
	sal AS sunit
JOIN
	dm.dim_artgld_details mstr
	ON
		mstr.slad_dmat_id = sunit.slsms_dmat_id AND
		--mstr.cntr_id IN (${COUNTRY_ID_LIST}) AND
		mstr.cntr_id IN (1,2,4) AND
		mstr.cntr_id = sunit.slsms_cntr_id
JOIN
	hier AS hier
	ON
		mstr.dmat_div_code = hier.div_code AND
		mstr.dmat_dep_code = hier.dep_code AND
		mstr.dmat_sec_code = hier.sec_code AND
		mstr.dmat_grp_code = hier.grp_code AND
		mstr.dmat_sgr_code = hier.sgr_code
JOIN
	dm.dim_stores stores
	ON
		stores.cntr_id = sunit.slsms_cntr_id AND
		stores.dmst_store_id = sunit.slsms_dmst_id AND
		--stores.cntr_id IN (${COUNTRY_ID_LIST}) AND
		stores.cntr_id IN (1,2,4) AND
		stores.convenience IN ('Convenience', 'HM') --AND
		--stores.dmst_store_code = '21014'
WHERE
	sunit.slsms_unit > 0 AND
	sunit.slsms_salex > 0
ORDER BY
	stores.dmst_store_code,
	hier.pmg,
	mstr.slad_tpnb;




-- select country, store, count(*) from sch_analysts.tbl_ce_daily_avg_sales_phrubos group by country, store order by country, store;

